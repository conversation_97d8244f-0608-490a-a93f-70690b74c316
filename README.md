# LiveNow - iOS应用

## 项目概述
LiveNow是一个现代化的iOS应用，专注于社交和匹配功能。应用采用Tab Bar导航结构，为用户提供便捷的多功能体验。

## 功能特性
本应用包含四个主要功能模块：

### 1. 首页 (Home) 🏠
- 展示最新动态和推荐内容
- 用户可以浏览热门内容
- 提供搜索和发现功能

### 2. 匹配 (Match) 💝
- 智能匹配算法
- 用户可以查看匹配结果
- 支持筛选和偏好设置

### 3. 消息聊天 (Messages) 💬
- 实时聊天功能
- 消息列表和聊天详情
- 支持文字、图片等多媒体消息

### 4. 我的 (Profile) 👤
- 个人资料管理
- 设置和偏好配置
- 账户信息和隐私设置

#### 新增：个人信息数据页（LNPersonalDataViewController）
- 入口：我的 -> Personal Data
- 功能：头像选择、姓名编辑、生日选择、国家选择、性别选择、保存
- 技术：UIKit + SnapKit，支持 iOS 13，动态字体和深色模式
- 注意：如需从相册选择头像，请在 Info.plist 增加 NSPhotoLibraryUsageDescription（相册访问用途说明）


#### 新增：设置页（LNSettingViewController）
- 入口：我的 -> Setting
- 功能：免打扰开关、黑名单、帮助与反馈、删除账号、协议与关于、版本显示、清理缓存、退出登录
- 技术：UIKit + SnapKit；iOS 13 动态字体与深色模式

## 技术架构
- **开发语言**: Swift
- **UI框架**: UIKit
- **布局工具**: SnapKit ✅ (已集成使用)
- **图片加载**: Kingfisher ✅ (已封装LNImageLoader)
- **响应式编程**: RxSwift (计划使用)
- **数据存储**: Core Data / SwiftData (计划使用)
- **设计规范**: 遵循 Apple Human Interface Guidelines
- **图标系统**: SF Symbols
- **命名规范**: 所有类使用LN前缀

## 项目结构
```
LiveNow/
├── AppDelegate.swift             # 应用委托
├── SceneDelegate.swift           # 场景委托
├── ViewControllers/             # 视图控制器（LN前缀命名）
│   ├── LNTabBarController.swift  # 主Tab控制器
│   ├── LNHomeViewController.swift # 首页
│   ├── LNMatchViewController.swift # 匹配页
│   ├── LNMessagesViewController.swift # 消息页
│   └── LNProfileViewController.swift # 个人页
├── Views/                       # 自定义视图
├── Models/                      # 数据模型
└── Resources/                  # 资源文件
    ├── Assets.xcassets         # 图片资源
    └── Base.lproj/            # 本地化文件
```

## 开发规范
- 使用Auto Layout和SnapKit进行界面布局
- 遵循MVC/MVVM架构模式
- 代码注释完整，可读性强
- 支持iPhone和iPad自适应布局
- 优化内存使用和性能

## 全局字体使用
本项目已集成自定义字体 HarmonyOS Sans（Regular/Medium/Bold/Black），并统一在代码层显式指定字体：

- 在 `Info.plist` 中通过 `UIAppFonts` 注册以下字体文件：
  - `HarmonyOS_Sans_Regular.ttf`
  - `HarmonyOS_Sans_Medium.ttf`
  - `HarmonyOS_Sans_Bold.ttf`
  - `HarmonyOS_Sans_Black.ttf`
- 在 `AppDelegate` 的 `application(_:didFinishLaunchingWithOptions:)` 中调用 `LNFontManager.shared.activate()` 完成外观设置（不做运行时递归替换）。
- 常用便捷方法（支持动态字体）：

```swift
// 固定字号
titleLabel.font = LNFont.medium(18)
bodyLabel.font = LNFont.regular(16)
strongLabel.font = LNFont.bold(16)

// 基于 TextStyle 的可缩放字体
let headline = LNFont.forTextStyle(.headline, weight: .medium)
let body = LNFont.forTextStyle(.body)
```

> 说明：项目不使用运行时遍历/方法交换来替换字体，所有字体均在代码中通过 `LNFont` 显式设置。

## 版本信息
- **当前版本**: 1.0.0
- **最低支持**: iOS 13.0+
- **开发工具**: Xcode 15+

## 数据模型架构

### 主播列表数据模型 (2025/8/23 更新)
项目已完成主播列表相关数据模型的重构，以完全匹配API返回的数据格式：

#### 核心模型
- **LNAnchorModel**: 直接匹配API返回的主播数据结构，同时用于UI显示
- **LNAnchorListResponse**: 分页响应数据模型
- **LNAnchorApiResponse**: 完整的API响应模型

#### 支持的API接口
- `popularList` - 热门主播列表
- `newList` - 最新主播列表
- `followList` - 关注主播列表

#### 数据流程（简化后）
```
API Response → LNAnchorModel → UI Display
```

#### 主要特性
- ✅ 完全匹配API数据结构
- ✅ 自动类型转换和默认值处理
- ✅ 计算属性封装业务逻辑
- ✅ 简化的单一数据模型架构
- ✅ HandyJSON自动解析支持

详细文档：`LiveNow/Home/Model/README-DataModel-Update.md`

## 图片加载工具 (2025/8/23 新增)

### LNImageLoader - 统一图片加载工具
基于Kingfisher封装的统一图片加载解决方案：

#### 核心特性
- ✅ **多种类型支持**: 头像、缩略图、封面图、普通图片
- ✅ **自动优化处理**: 根据图片类型自动应用合适的处理选项
- ✅ **完善缓存管理**: 内存缓存100MB，磁盘缓存500MB，7天过期
- ✅ **便捷扩展方法**: UIImageView扩展，使用更简单
- ✅ **占位图支持**: 支持自定义占位图和fallback机制

#### 使用示例
```swift
// 加载头像（自动圆角）
avatarImageView.ln_setAvatar(url: "https://example.com/avatar.jpg")

// 加载缩略图（压缩处理）
thumbnailImageView.ln_setThumbnail(url: "https://example.com/thumb.jpg")

// 加载封面图（高质量）
coverImageView.ln_setCover(url: "https://example.com/cover.jpg")

// 缓存管理
LNImageLoader.clearCache()
LNImageLoader.preloadImages(urls)
```

详细文档：`LiveNow/Document/LNImageLoader_README.md`

## 页面导航功能 (2025/8/23 新增)

### "谁喜欢我"页面多入口导航
实现了从多个位置点击跳转到"谁喜欢我"页面的功能：

#### 支持的入口点
- ✅ **首页爱心按钮**: 点击首页右上角爱心图标
- ✅ **消息页卡片**: 点击消息页面的"who liked me"卡片
- ✅ **会员中心按钮**: 点击会员中心的"Check who liked me"按钮
- ✅ **个人资料菜单**: 点击个人资料页面的菜单项

#### 统一的跳转特性
- 自动隐藏底部TabBar
- 使用标准push动画
- 支持返回手势和按钮
- 保持导航栏样式一致

详细文档：`LiveNow/Document/WhoLikedMe_Navigation_README.md`

## 开发进度
- [✅] 项目初始化
- [✅] Tab Bar结构搭建（使用LN前缀）
- [✅] 各页面功能实现（使用LN前缀）
- [✅] 界面美化和交互优化
- [✅] SnapKit约束库集成
- [✅] 统一命名规范（LN前缀）
- [✅] 主播列表数据模型重构
- [✅] API数据解析优化
- [✅] 图片加载工具封装（LNImageLoader）
- [✅] Kingfisher集成和配置
- [✅] "谁喜欢我"页面导航功能
- [⏳] 数据存储和网络请求
- [⏳] 高级功能实现

---
*LiveNow - 让生活更精彩* ✨