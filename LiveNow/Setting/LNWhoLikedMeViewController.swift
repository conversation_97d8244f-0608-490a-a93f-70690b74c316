//
//  LNWhoLikedMeViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/14.
//

import UIKit
import SnapKit
import MJRefresh
import HandyJSON

/// "Who Liked Me" 页面
/// 显示喜欢当前用户的用户列表，每个用户项包含头像、姓名、年龄性别标签、国家标签和视频通话按钮
class LNWhoLikedMeViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }

    // MARK: - Data
    private var data: [LNWhoLikedMeUser] = []

    // 与 LNBlacklistViewController 保持一致的分页/刷新配置
    private var current: Int = 1
    private let size: Int = 10
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.systemGroupedBackground
        tv.register(LNWhoLikedMeCell.self, forCellReuseIdentifier: LNWhoLikedMeCell.reuseId)
        tv.rowHeight = s(68)

        // 配置空状态
        tv.em.emptyView = LNEmptyView.empty(
            firstReloadHidden: true,
            canTouch: false,
            isUserInteractionEnabled: false,
            offsetY: -s(80),
            space: s(15),
            backColor: .clear
        ) { config in
            config.image = UIImage(named: "ic_empty")
            config.imageSize = CGSize(width: s(120), height: s(120))
            config.titleTopSpace = s(20)
            config.title = "No one has liked you yet."
            config.titleFont = LNFont.regular(16)
            config.titleColor = UIColor.systemGray
        } closure: { tag in
            print("Empty view tapped with tag: \(tag)")
        }

        return tv
    }()

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "who liked me"
        edgesForExtendedLayout = .all
        setupUI()
        setupRefresh()
        loadData(reset: true)
    }

    private func setupUI() {
        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
        // 初始时隐藏footer，直到有数据时才显示
        tableView.mj_footer?.isHidden = true
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            current = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 调用谁喜欢我接口
        let params = [
            "current": current,
            "size": size
        ]

        NetWorkRequest(LNApiProfile.whoSeeMe(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析响应数据
            if let dataArray = result["data"] as? [[String: Any]] {
                // 直接解析数组数据
                let newUsers = dataArray.compactMap { userDict -> LNWhoLikedMeUser? in
                    return LNWhoLikedMeUser.deserialize(from: userDict)
                }

                if reset {
                    self.data = newUsers
                } else {
                    self.data.append(contentsOf: newUsers)
                }

                // 更新分页信息（由于API返回的是数组，我们根据返回数量判断是否还有更多数据）
                self.current += 1
                self.hasMore = newUsers.count >= self.size

                DispatchQueue.main.async {
                    self.tableView.reloadData()
                    self.tableView.mj_header?.endRefreshing()

                    // 只有当有数据时才处理footer状态
                    if self.data.isEmpty {
                        // 数据为空时，隐藏footer
                        self.tableView.mj_footer?.endRefreshing()
                        self.tableView.mj_footer?.isHidden = true
                    } else {
                        // 有数据时，显示footer并根据是否还有更多数据设置状态
                        self.tableView.mj_footer?.isHidden = false
                        if self.hasMore {
                            self.tableView.mj_footer?.endRefreshing()
                        } else {
                            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                        }
                    }
                }
            } else {
                // 解析失败，结束刷新
                DispatchQueue.main.async {
                    self.tableView.mj_header?.endRefreshing()
                    self.tableView.mj_footer?.endRefreshing()
                    // 如果数据为空，隐藏footer
                    if self.data.isEmpty {
                        self.tableView.mj_footer?.isHidden = true
                    }
                }
            }
        }, failure: { [weak self] error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()

                // 如果数据为空，隐藏footer
                if self.data.isEmpty {
                    self.tableView.mj_footer?.isHidden = true
                }

                // 显示错误提示
                print("谁喜欢我列表加载失败: \(error.localizedDescription)")
            }
        })
    }

    // MARK: - Table
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { 
        return data.count 
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNWhoLikedMeCell.reuseId, for: indexPath) as! LNWhoLikedMeCell
        cell.configure(with: data[indexPath.row])
        cell.onVideoCall = { [weak self] in
            guard let self = self else { return }
            // 处理视频通话逻辑
            self.handleVideoCall(for: self.data[indexPath.row])
        }
        return cell
    }
    
    // MARK: - Actions
    private func handleVideoCall(for user: LNWhoLikedMeUser) {
        // 这里可以集成视频通话功能
        let alert = UIAlertController(title: "Video Call", message: "Start video call with \(user.displayName)?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Call", style: .default) { _ in
            // 启动视频通话
            print("Starting video call with \(user.displayName), userId: \(user.id)")
        })
        present(alert, animated: true)
    }
}
